using System;
using System.Collections.Generic;
using System.Linq;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Centralized configuration for symbols that cause database caching issues.
/// These symbols are excluded from database caching operations to prevent system errors.
/// </summary>
public static class ProblematicSymbolsConfig
{
    /// <summary>
    /// List of symbols that cause database caching issues and should be excluded from caching operations.
    /// These symbols will still be traded but their bar data will be fetched from APIs each time.
    /// </summary>
    public static readonly HashSet<string> ProblematicSymbols = new(StringComparer.OrdinalIgnoreCase)
    {
        "BELFB",  // Known database format issues
        "BATRA",  // Database caching errors reported in Discord
        "ARKW"    // Database caching errors reported in Discord
    };

    /// <summary>
    /// Checks if a symbol is known to cause database caching issues.
    /// </summary>
    /// <param name="symbol">The symbol to check</param>
    /// <returns>True if the symbol should be excluded from database caching</returns>
    public static bool IsProblematicSymbol(string symbol)
    {
        if (string.IsNullOrWhiteSpace(symbol))
            return false;

        return ProblematicSymbols.Contains(symbol);
    }

    /// <summary>
    /// Gets all problematic symbols as an array for logging purposes.
    /// </summary>
    /// <returns>Array of problematic symbols</returns>
    public static string[] GetProblematicSymbolsArray()
    {
        return ProblematicSymbols.ToArray();
    }

    /// <summary>
    /// Adds a new problematic symbol to the list (for runtime detection).
    /// Note: This is for runtime detection only. Permanent additions should be made to the static list.
    /// </summary>
    /// <param name="symbol">Symbol to add</param>
    /// <returns>True if the symbol was added, false if it was already in the list</returns>
    public static bool AddProblematicSymbol(string symbol)
    {
        if (string.IsNullOrWhiteSpace(symbol))
            return false;

        return ProblematicSymbols.Add(symbol.ToUpperInvariant());
    }
}
